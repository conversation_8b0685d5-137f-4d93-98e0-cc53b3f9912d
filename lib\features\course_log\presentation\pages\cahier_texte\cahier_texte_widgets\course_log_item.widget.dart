import 'package:flutter/material.dart';
import 'package:Kairos/features/course_log/domain/entities/course_log_entity.dart';


class CourseLogItem extends StatelessWidget {
  final CourseLogEntity courseLog;

  const CourseLogItem({super.key, required this.courseLog});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // TODO: Update CourseLogDetailsDialog to use CourseLogEntity
        // showDialog(
        //   context: context,
        //   builder: (BuildContext context) {
        //     return CourseLogDetailsDialog(courseLog: courseLog);
        //   },
        // );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 1.0),
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column( // Use Column as the main layout widget
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row( // Row 1: Time, Professor, Duration
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded( // Use Expanded for the time and professor text
                    child: Text(
                      '${courseLog.heureDebutPrevu} - ${courseLog.heureFinPrevu} | ${courseLog.professeur}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Row( // Row for clock icon and duration
                    children: [
                      const Icon(Icons.access_time, size: 14, color: Colors.black), // Clock icon
                      const SizedBox(width: 4),
                      Text(
                        courseLog.dureeSaisi,
                        style: const TextStyle(fontSize: 14, color: Colors.black),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 4), // Spacing between rows
              Text( // Row 2: Course name
                courseLog.cours,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4), // Spacing between rows
              Row( // Row 3: Author, Date, Class, Semester
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded( // Use Expanded for author and date
                    child: Text(
                      'Enregistré par ${courseLog.auteurSaisi} | ${_formatDate(courseLog.dateCours)}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text( // Class and Semester
                    '${courseLog.classe} | ${courseLog.semestre}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to format date as dd/mm
  String _formatDate(String dateString) {
    try {
      final dateParts = dateString.split('-');
      if (dateParts.length >= 3) {
        return '${dateParts[2]}/${dateParts[1]}';
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }
}
